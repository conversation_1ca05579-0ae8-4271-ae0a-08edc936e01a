# Natural Language Toolkit: ASCII visualization of NLTK trees
#
# Copyright (C) 2001-2024 NLTK Project
# Author: <PERSON> <<PERSON><PERSON><PERSON><PERSON>@uva.nl>
#         <PERSON> <<EMAIL>>
# URL: <https://www.nltk.org/>
# For license information, see LICENSE.TXT

"""
Pretty-printing of discontinuous trees.
Adapted from the disco-dop project, by <PERSON>.
https://github.com/andreasvc/disco-dop

Interesting reference (not used for this code):
<PERSON><PERSON> et al., Orth. Hypergraph Drawing, Journal of
Graph Algorithms and Applications, 10(2) 141--157 (2006)149.
https://jgaa.info/accepted/2006/EschbachGuentherBecker2006.10.2.pdf
"""

from nltk.internals import Deprecated
from nltk.tree.prettyprinter import TreePrettyPrinter as TPP


class TreePrettyPrinter(Deprecated, TPP):
    """Import `TreePrettyPrinter` using `from nltk.tree import TreePrettyPrinter` instead."""


__all__ = ["TreePrettyPrinter"]
