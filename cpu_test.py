import os
import torch
print("PyTorch version:", torch.__version__)
print("CUDA available:", torch.cuda.is_available())
print("CPU count:", torch.get_num_threads())

# Force CPU usage
os.environ['CUDA_VISIBLE_DEVICES'] = ''
torch.set_num_threads(1)

try:
    print("Importing sentence_transformers...")
    from sentence_transformers import SentenceTransformer
    print("✓ Import successful")
    
    print("Creating model instance with CPU device...")
    model = SentenceTransformer('all-MiniLM-L6-v2', device='cpu')
    print("✓ Model created")
    
    print("Testing with single sentence...")
    test_sentence = "This is a test"
    embedding = model.encode([test_sentence], device='cpu', show_progress_bar=False, convert_to_numpy=True)
    print(f"✓ Single embedding successful, shape: {embedding.shape}")
    
    print("Testing with multiple sentences...")
    test_sentences = ["This is test 1", "This is test 2"]
    embeddings = model.encode(test_sentences, device='cpu', show_progress_bar=False, batch_size=1, convert_to_numpy=True)
    print(f"✓ Multiple embeddings successful, shape: {embeddings.shape}")
    
    print("\n=== CPU TEST PASSED ===")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
