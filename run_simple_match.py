#!/usr/bin/env python3

import subprocess
import os

def run_simple_match_with_defaults():
    """Run match_simple.py with default inputs"""
    
    # Prepare the inputs
    inputs = [
        "",  # Use default R1.xlsx
        "",  # Use default Imran Sample Data.xlsx  
        "Matching_TfIdf_500cats_03-05-2025.xlsx",  # Custom output file
        "500"  # Limit to 500 categories for testing
    ]
    
    # Join inputs with newlines
    input_text = "\n".join(inputs) + "\n"
    
    # Get the Python executable path
    python_exe = os.path.join("venv_new", "Scripts", "python.exe")
    
    print("Running match_simple.py with default inputs...")
    print("Inputs being provided:")
    print("- OnBuy Categories file: R1.xlsx (default)")
    print("- Sample Data file: Imran Sample Data.xlsx (default)")
    print("- Output file: Matching_TfIdf_500cats_03-05-2025.xlsx")
    print("- Limit categories: 500 (for testing)")
    print()
    
    try:
        # Run the process
        process = subprocess.Popen(
            [python_exe, "match_simple.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        # Send inputs and get output
        stdout, stderr = process.communicate(input=input_text, timeout=300)  # 5 minute timeout
        
        print("STDOUT:")
        print(stdout)
        
        if stderr:
            print("\nSTDERR:")
            print(stderr)
            
        print(f"\nProcess completed with return code: {process.returncode}")
        
    except subprocess.TimeoutExpired:
        print("Process timed out after 5 minutes")
        process.kill()
    except Exception as e:
        print(f"Error running process: {e}")

if __name__ == "__main__":
    run_simple_match_with_defaults()
