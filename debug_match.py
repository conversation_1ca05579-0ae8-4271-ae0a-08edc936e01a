import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss

def test_small_embedding():
    """Test with a small subset of categories"""
    print("Testing with small dataset...")
    
    # Test categories
    test_categories = [
        "Electronics > Computers > Laptops",
        "Clothing > Men > Shirts", 
        "Home & Garden > Kitchen > Appliances",
        "Books > Fiction > Mystery",
        "Sports > Fitness > Equipment"
    ]
    
    print(f"Loading model...")
    model = SentenceTransformer('all-MiniLM-L6-v2')
    
    print(f"Creating embeddings for {len(test_categories)} test categories...")
    embeddings = model.encode(test_categories, show_progress_bar=True, batch_size=2)
    
    print(f"Embeddings shape: {embeddings.shape}")
    print("Building FAISS index...")
    
    dimension = embeddings.shape[1]
    index = faiss.IndexFlatL2(dimension)
    faiss.normalize_L2(embeddings)
    index.add(embeddings)
    
    print("Testing search...")
    # Test search
    query = "laptop computer"
    query_embedding = model.encode([query])
    faiss.normalize_L2(query_embedding)
    
    distances, indices = index.search(query_embedding, 3)
    
    print(f"\nQuery: '{query}'")
    print("Top matches:")
    for i in range(len(indices[0])):
        idx = indices[0][i]
        distance = distances[0][i]
        similarity = 1 - min(distance, 2.0) / 2.0
        print(f"  {i+1}. {test_categories[idx]} (similarity: {similarity:.3f})")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    test_small_embedding()
