#!/usr/bin/env python3

import pandas as pd
import numpy as np
import time
import os
from tqdm import tqdm
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

def load_onbuy_categories(reference_file):
    """Load all OnBuy categories from Excel file"""
    all_categories = []
    
    # Try to read all sheets (Sheet2 to Sheet17)
    for sheet_num in range(2, 18):
        sheet_name = f'Sheet{sheet_num}'
        try:
            df = pd.read_excel(reference_file, sheet_name=sheet_name)
            
            # Try to find the category column
            category_col = None
            for col in df.columns:
                if 'onbuy category' in col.lower() or 'category' in col.lower() or 'idonbuy' in col.lower():
                    category_col = col
                    break
                    
            if category_col:
                # Extract categories
                categories = df[category_col].astype(str).tolist()
                categories = [cat for cat in categories if cat and cat.lower() != 'nan']
                all_categories.extend(categories)
                print(f"Loaded {len(categories)} categories from {sheet_name}")
        except Exception as e:
            print(f"Error reading sheet {sheet_name}: {e}")
    
    return all_categories

def create_tfidf_index(categories):
    """Create TF-IDF vectors for categories"""
    print("Creating TF-IDF vectors...")
    
    # Create TF-IDF vectorizer
    vectorizer = TfidfVectorizer(
        lowercase=True,
        stop_words='english',
        ngram_range=(1, 2),  # Use unigrams and bigrams
        max_features=10000   # Limit features to avoid memory issues
    )
    
    # Fit and transform categories
    tfidf_matrix = vectorizer.fit_transform(categories)
    
    print(f"TF-IDF matrix shape: {tfidf_matrix.shape}")
    return vectorizer, tfidf_matrix

def match_amazon_category(amazon_category, vectorizer, tfidf_matrix, all_categories, top_k=1):
    """Find the best matching OnBuy category for an Amazon category"""
    try:
        # Transform the Amazon category using the same vectorizer
        query_vector = vectorizer.transform([amazon_category])
        
        # Calculate cosine similarity
        similarities = cosine_similarity(query_vector, tfidf_matrix).flatten()
        
        # Get top matches
        top_indices = similarities.argsort()[-top_k:][::-1]
        
        results = []
        for idx in top_indices:
            similarity = similarities[idx]
            results.append((all_categories[idx], similarity))
        
        if results:
            return results[0][0], results[0][1]
        else:
            return "No match found", 0.0
            
    except Exception as e:
        print(f"Error matching '{amazon_category}': {e}")
        return f"Error: {str(e)}", 0.0

def process_sample_file(sample_file, vectorizer, tfidf_matrix, all_categories, output_file=None):
    """Process sample data file and match categories"""
    if output_file is None:
        output_file = 'Imran_Sample_Data_Matched_TfIdf.xlsx'
    
    # Load sample data
    sample_data = pd.read_excel(sample_file)
    
    # Find category column
    if 'Categories' not in sample_data.columns:
        potential_cols = [col for col in sample_data.columns if 'categ' in col.lower()]
        if potential_cols:
            print(f"'Categories' column not found. Using '{potential_cols[0]}' instead.")
            sample_data.rename(columns={potential_cols[0]: 'Categories'}, inplace=True)
        else:
            raise ValueError("'Categories' column not found in sample data file")
    
    # Process each row
    matched_categories = []
    match_scores = []
    
    print("Matching categories...")
    for idx, row in enumerate(tqdm(sample_data.iterrows(), total=len(sample_data))):
        _, row_data = row
        amazon_category = str(row_data['Categories'])
        
        if amazon_category.lower() == 'nan' or not amazon_category:
            matched_categories.append("No category provided")
            match_scores.append(0.0)
            continue
        
        # Match category
        matched_category, score = match_amazon_category(amazon_category, vectorizer, tfidf_matrix, all_categories)
        matched_categories.append(matched_category)
        match_scores.append(score)
    
    # Add results to sample data
    sample_data['Matched OnBuy Category'] = matched_categories
    sample_data['Match Score'] = match_scores
    
    # Add root category (extract from matched category)
    root_categories = []
    for cat in matched_categories:
        if '>' in cat:
            root_categories.append(cat.split('>')[0].strip())
        else:
            root_categories.append("")
    
    sample_data['Matched OnBuy Root'] = root_categories
    
    # Save results
    sample_data.to_excel(output_file, index=False)
    print(f"Results saved to '{output_file}'")

def main():
    print("=== Amazon to OnBuy Category Matcher using TF-IDF ===")

    # Get input files
    reference_file = input("Enter path to OnBuy Categories Excel file (default: 'R1.xlsx'): ") or 'R1.xlsx'
    sample_file = input("Enter path to Sample Data Excel file (default: 'Imran Sample Data.xlsx'): ") or 'Imran Sample Data.xlsx'
    output_file = input("Enter path for output file (default: 'Matching_TfIdf_03-05-2025.xlsx'): ") or 'Matching_TfIdf_03-05-2025.xlsx'

    # Ask if user wants to limit categories for testing
    limit_input = input("Limit categories for testing? Enter number (default: no limit): ").strip()
    limit_categories = None
    if limit_input:
        try:
            limit_categories = int(limit_input)
            print(f"Will limit to first {limit_categories} categories for testing")
        except ValueError:
            print("Invalid number, using all categories")

    # Load categories
    print("Loading OnBuy categories...")
    categories = load_onbuy_categories(reference_file)

    if limit_categories:
        categories = categories[:limit_categories]
        print(f"Limited to {len(categories)} categories for testing")

    # Create TF-IDF index
    vectorizer, tfidf_matrix = create_tfidf_index(categories)

    # Process sample file
    process_sample_file(sample_file, vectorizer, tfidf_matrix, categories, output_file)

    print("Process complete!")

if __name__ == "__main__":
    main()
