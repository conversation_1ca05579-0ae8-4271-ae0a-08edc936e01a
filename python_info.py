import sys
import os

print("=== Python Environment Information ===")
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path}")
print(f"Current working directory: {os.getcwd()}")

print("\n=== Virtual Environment Check ===")
if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
    print("✓ Running in virtual environment")
    print(f"Virtual env prefix: {sys.prefix}")
    if hasattr(sys, 'base_prefix'):
        print(f"Base prefix: {sys.base_prefix}")
else:
    print("❌ NOT running in virtual environment")

print("\n=== Package Installation Check ===")
try:
    import sentence_transformers
    print(f"✓ sentence_transformers found at: {sentence_transformers.__file__}")
    print(f"✓ sentence_transformers version: {sentence_transformers.__version__}")
except ImportError as e:
    print(f"❌ sentence_transformers not found: {e}")

try:
    import faiss
    print(f"✓ faiss found at: {faiss.__file__}")
except ImportError as e:
    print(f"❌ faiss not found: {e}")

try:
    import pandas
    print(f"✓ pandas found at: {pandas.__file__}")
    print(f"✓ pandas version: {pandas.__version__}")
except ImportError as e:
    print(f"❌ pandas not found: {e}")

print("\n=== Recommended VS Code Python Interpreter ===")
venv_python = os.path.join(os.getcwd(), "venv", "Scripts", "python.exe")
d_elate_python = r"d:\elate\venv\Scripts\python.exe"

print(f"Local venv path: {venv_python}")
print(f"D: drive venv path: {d_elate_python}")

if os.path.exists(venv_python):
    print(f"✓ Local venv Python exists: {venv_python}")
else:
    print(f"❌ Local venv Python not found: {venv_python}")

if os.path.exists(d_elate_python):
    print(f"✓ D: drive venv Python exists: {d_elate_python}")
    print(f"\n🔧 SOLUTION: Set VS Code Python interpreter to: {d_elate_python}")
else:
    print(f"❌ D: drive venv Python not found: {d_elate_python}")

print("\n=== Instructions to Fix VS Code ===")
print("1. Open VS Code")
print("2. Press Ctrl+Shift+P")
print("3. Type 'Python: Select Interpreter'")
print("4. Choose 'Enter interpreter path...'")
print(f"5. Enter: {d_elate_python}")
print("6. Press Enter")
print("7. Reload VS Code window (Ctrl+Shift+P -> 'Developer: Reload Window')")
