from sentence_transformers import SentenceTransformer

model = SentenceTransformer('all-MiniLM-L6-v2')
sentences = ["This is a test sentence", "This is another sentence"]

print("Encoding sentences...")
try:
    embeddings = model.encode(sentences, show_progress_bar=True, batch_size=2)
    print("Encoding complete!")
    print(f"Embeddings shape: {embeddings.shape}")
except Exception as e:
    print("Error during encoding:", e)