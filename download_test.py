import os
from huggingface_hub import snapshot_download
import torch

# Force CPU and single thread
os.environ['CUDA_VISIBLE_DEVICES'] = ''
torch.set_num_threads(1)

try:
    print("Attempting to download model manually...")
    model_name = "sentence-transformers/all-MiniLM-L6-v2"
    
    # Try to download the model files
    cache_dir = snapshot_download(repo_id=model_name)
    print(f"✓ Model downloaded to: {cache_dir}")
    
    print("Now testing sentence transformers...")
    from sentence_transformers import SentenceTransformer
    
    # Try to load from cache
    model = SentenceTransformer(model_name, device='cpu', cache_folder=cache_dir)
    print("✓ Model loaded from cache")
    
    # Test encoding
    print("Testing encoding...")
    result = model.encode("test", show_progress_bar=False)
    print(f"✓ Encoding successful: {result.shape}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    
    # Try alternative approach
    print("\nTrying alternative approach...")
    try:
        from sentence_transformers import SentenceTransformer
        import torch
        
        # Try with a very simple setup
        model = SentenceTransformer('all-MiniLM-L6-v2')
        model.eval()
        
        with torch.no_grad():
            # Try manual tokenization and encoding
            print("Trying manual approach...")
            sentences = ["test"]
            features = model.tokenize(sentences)
            embeddings = model.forward(features)
            print(f"✓ Manual encoding successful: {embeddings['sentence_embedding'].shape}")
            
    except Exception as e2:
        print(f"❌ Alternative approach failed: {e2}")
        traceback.print_exc()
