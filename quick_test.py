import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss

def quick_test():
    """Quick test with just 10 categories"""
    print("=== Quick Test ===")
    
    # Load just a few categories from the Excel file
    try:
        df = pd.read_excel('R1.xlsx', sheet_name='Sheet2')
        
        # Find the category column
        category_col = None
        for col in df.columns:
            if 'onbuy category' in col.lower() or 'category' in col.lower() or 'idonbuy' in col.lower():
                category_col = col
                break
        
        if category_col:
            categories = df[category_col].astype(str).tolist()
            categories = [cat for cat in categories if cat and cat.lower() != 'nan']
            # Take only first 10 for testing
            categories = categories[:10]
            print(f"Loaded {len(categories)} test categories:")
            for i, cat in enumerate(categories, 1):
                print(f"  {i}. {cat}")
        else:
            print("No category column found")
            return
            
    except Exception as e:
        print(f"Error loading categories: {e}")
        return
    
    # Load model
    print("\nLoading embedding model...")
    try:
        model = SentenceTransformer('all-MiniLM-L6-v2')
        print("Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Create embeddings
    print(f"\nCreating embeddings for {len(categories)} categories...")
    try:
        embeddings = model.encode(categories, show_progress_bar=True, batch_size=2, convert_to_numpy=True)
        print(f"Embeddings created successfully! Shape: {embeddings.shape}")
    except Exception as e:
        print(f"Error creating embeddings: {e}")
        return
    
    # Build FAISS index
    print("\nBuilding FAISS index...")
    try:
        dimension = embeddings.shape[1]
        index = faiss.IndexFlatL2(dimension)
        faiss.normalize_L2(embeddings)
        index.add(embeddings)
        print("FAISS index built successfully!")
    except Exception as e:
        print(f"Error building index: {e}")
        return
    
    # Test search
    print("\nTesting search...")
    test_queries = ["laptop computer", "men's clothing", "kitchen appliance"]
    
    for query in test_queries:
        try:
            query_embedding = model.encode([query])
            faiss.normalize_L2(query_embedding)
            distances, indices = index.search(query_embedding, 3)
            
            print(f"\nQuery: '{query}'")
            print("Top matches:")
            for i in range(min(3, len(indices[0]))):
                idx = indices[0][i]
                distance = distances[0][i]
                similarity = 1 - min(distance, 2.0) / 2.0
                print(f"  {i+1}. {categories[idx]} (similarity: {similarity:.3f})")
        except Exception as e:
            print(f"Error searching for '{query}': {e}")
    
    print("\n=== Quick test completed successfully! ===")

if __name__ == "__main__":
    quick_test()
