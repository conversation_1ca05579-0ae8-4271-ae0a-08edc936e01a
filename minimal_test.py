import os
import sys
print("Python version:", sys.version)
print("Current directory:", os.getcwd())

try:
    print("Importing sentence_transformers...")
    from sentence_transformers import SentenceTransformer
    print("✓ Import successful")
    
    print("Creating model instance...")
    model = SentenceTransformer('all-MiniLM-L6-v2')
    print("✓ Model created")
    
    print("Testing with single sentence...")
    test_sentence = "This is a test"
    embedding = model.encode([test_sentence], show_progress_bar=False)
    print(f"✓ Single embedding successful, shape: {embedding.shape}")
    
    print("Testing with multiple sentences...")
    test_sentences = ["This is test 1", "This is test 2", "This is test 3"]
    embeddings = model.encode(test_sentences, show_progress_bar=False, batch_size=1)
    print(f"✓ Multiple embeddings successful, shape: {embeddings.shape}")
    
    print("Testing with progress bar...")
    embeddings = model.encode(test_sentences, show_progress_bar=True, batch_size=1)
    print(f"✓ Progress bar test successful, shape: {embeddings.shape}")
    
    print("\n=== ALL TESTS PASSED ===")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
