#!/usr/bin/env python3

print("Starting import test...")

print("1. Importing pandas...")
import pandas as pd
print("   ✓ pandas imported successfully")

print("2. Importing numpy...")
import numpy as np
print("   ✓ numpy imported successfully")

print("3. Importing tqdm...")
from tqdm import tqdm
print("   ✓ tqdm imported successfully")

print("4. Importing sentence_transformers...")
try:
    from sentence_transformers import SentenceTransformer
    print("   ✓ sentence_transformers imported successfully")
except Exception as e:
    print(f"   ✗ Error importing sentence_transformers: {e}")
    import traceback
    traceback.print_exc()

print("5. Importing faiss...")
try:
    import faiss
    print("   ✓ faiss imported successfully")
except Exception as e:
    print(f"   ✗ Error importing faiss: {e}")
    import traceback
    traceback.print_exc()

print("6. Importing openpyxl...")
try:
    import openpyxl
    print("   ✓ openpyxl imported successfully")
except Exception as e:
    print(f"   ✗ Error importing openpyxl: {e}")
    import traceback
    traceback.print_exc()

print("All import tests completed!")

# Test basic functionality
print("\n7. Testing basic functionality...")
try:
    model = SentenceTransformer('all-MiniLM-L6-v2')
    print("   ✓ SentenceTransformer model loaded successfully")
except Exception as e:
    print(f"   ✗ Error loading SentenceTransformer model: {e}")
    import traceback
    traceback.print_exc()

print("Test script completed!")
