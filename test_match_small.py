import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss

def load_small_categories():
    """Load just 50 categories for testing"""
    try:
        df = pd.read_excel('R1.xlsx', sheet_name='Sheet2')
        
        # Find the category column
        category_col = None
        for col in df.columns:
            if 'onbuy category' in col.lower() or 'category' in col.lower() or 'idonbuy' in col.lower():
                category_col = col
                break
        
        if category_col:
            categories = df[category_col].astype(str).tolist()
            categories = [cat for cat in categories if cat and cat.lower() != 'nan']
            # Take only first 50 for testing
            categories = categories[:50]
            print(f"Loaded {len(categories)} test categories")
            return categories
        else:
            print("No category column found")
            return []
            
    except Exception as e:
        print(f"Error loading categories: {e}")
        return []

def create_embeddings_small(categories):
    """Create embeddings for small dataset"""
    print("Loading embedding model...")
    model = SentenceTransformer('all-MiniLM-L6-v2', device='cpu')
    
    print(f"Creating embeddings for {len(categories)} categories...")
    embeddings = model.encode(categories, show_progress_bar=True, batch_size=8, convert_to_numpy=True)
    
    print(f"Embeddings shape: {embeddings.shape}")
    
    # Build FAISS index
    print("Building FAISS index...")
    dimension = embeddings.shape[1]
    index = faiss.IndexFlatL2(dimension)
    faiss.normalize_L2(embeddings)
    index.add(embeddings)
    
    return model, index, categories

def test_matching(model, index, categories):
    """Test the matching functionality"""
    print("\nTesting category matching...")
    
    test_queries = [
        "laptop computer electronics",
        "men's clothing shirts",
        "kitchen appliances",
        "art paper crafts"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        
        # Create embedding for query
        query_embedding = model.encode([query])
        faiss.normalize_L2(query_embedding)
        
        # Search
        distances, indices = index.search(query_embedding, 3)
        
        print("Top matches:")
        for i in range(min(3, len(indices[0]))):
            idx = indices[0][i]
            distance = distances[0][i]
            similarity = 1 - min(distance, 2.0) / 2.0
            print(f"  {i+1}. {categories[idx]} (similarity: {similarity:.3f})")

def main():
    print("=== Small Scale Test ===")
    
    # Load small set of categories
    categories = load_small_categories()
    if not categories:
        print("Failed to load categories")
        return
    
    # Create embeddings
    model, index, categories = create_embeddings_small(categories)
    
    # Test matching
    test_matching(model, index, categories)
    
    print("\n=== Test completed successfully! ===")

if __name__ == "__main__":
    main()
